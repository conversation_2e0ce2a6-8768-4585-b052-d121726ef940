#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC简化自动签到脚本
功能：最简单的MSEC平台自动签到，只保留核心网络请求
"""

import requests
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any, List
from simple_token_storage import UnifiedTokenStorage


def get_week_checkin_params() -> Tuple[str, int]:
    """获取本周签到历史查询参数"""
    today = datetime.now()
    weekday = today.weekday()
    monday = today - timedelta(days=weekday)
    start_date = monday.strftime('%Y-%m-%d')
    days = weekday + 1
    return start_date, days


def parse_env_users() -> List[Tuple[str, str]]:
    """
    从环境变量解析多用户配置

    Returns:
        用户名密码对列表 [(username, password), ...]
    """
    users_env = os.getenv('MSEC_USER', '').strip()
    passwords_env = os.getenv('MSEC_PASS', '').strip()

    if not users_env or not passwords_env:
        return []

    # 分割用户名和密码
    usernames = [u.strip() for u in users_env.split(',') if u.strip()]
    passwords = [p.strip() for p in passwords_env.split(',') if p.strip()]

    # 检查数量是否匹配
    if len(usernames) != len(passwords):
        print(f"⚠️ 用户名数量({len(usernames)})与密码数量({len(passwords)})不匹配")
        return []

    return list(zip(usernames, passwords))


def get_yunma_token() -> Optional[str]:
    """从环境变量获取云码Token"""
    return os.getenv('YUNMA_TOKEN', '').strip() or None


class MultiUserResult:
    """多用户执行结果"""

    def __init__(self):
        self.total_users = 0
        self.success_users: List[str] = []
        self.failed_users: List[Tuple[str, str]] = []  # (username, error)
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None

    def add_success(self, username: str):
        """添加成功用户"""
        self.success_users.append(username)

    def add_failure(self, username: str, error: str):
        """添加失败用户"""
        self.failed_users.append((username, error))

    def finish(self):
        """完成执行"""
        self.end_time = datetime.now()

    def get_summary(self) -> str:
        """获取执行摘要"""
        duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0

        summary = [
            "=" * 50,
            "📊 多用户签到执行报告",
            "=" * 50,
            f"⏱️ 执行时间: {duration:.1f}秒",
            f"👥 总用户数: {self.total_users}",
            f"✅ 成功用户: {len(self.success_users)}",
            f"❌ 失败用户: {len(self.failed_users)}",
            ""
        ]

        if self.success_users:
            summary.append("✅ 成功用户列表:")
            for user in self.success_users:
                summary.append(f"   • {user}")
            summary.append("")

        if self.failed_users:
            summary.append("❌ 失败用户列表:")
            for user, error in self.failed_users:
                summary.append(f"   • {user}: {error}")
            summary.append("")

        summary.append("=" * 50)
        return "\n".join(summary)


def run_user_checkin(users: List[Tuple[str, str]], yunma_token: Optional[str] = None,
                          max_captcha_refresh: int = 2) -> MultiUserResult:
    """
    运行多用户签到

    Args:
        users: 用户名密码对列表
        yunma_token: 云码验证码识别Token
        max_captcha_refresh: 验证码刷新次数

    Returns:
        执行结果
    """
    result = MultiUserResult()
    result.total_users = len(users)

    print("🚀 开始MSEC自动签到")
    print("=" * 50)
    print(f"👥 总用户数: {len(users)}")
    print(f"🤖 验证码识别: {'启用' if yunma_token else '禁用'}")
    print(f"🔄 验证码刷新次数: {max_captcha_refresh}")
    print("=" * 50)

    for i, (username, password) in enumerate(users, 1):
        print(f"\n📋 处理用户 {i}/{len(users)}: {username}")
        print("-" * 30)

        try:
            # 创建客户端
            client = MSECSimpleClient(
                username=username,
                password=password,
                enable_token_storage=True,
                captcha_token=yunma_token,
                enable_auto_captcha=bool(yunma_token),
                max_captcha_refresh=max_captcha_refresh
            )

            # 执行签到流程
            success = client.run_full_process()

            if success:
                result.add_success(username)
                print(f"✅ 用户 {username} 签到成功")
            else:
                result.add_failure(username, "签到流程失败")
                print(f"❌ 用户 {username} 签到失败")

        except Exception as e:
            error_msg = str(e)
            result.add_failure(username, error_msg)
            print(f"❌ 用户 {username} 处理异常: {error_msg}")

        # 用户间间隔，避免请求过于频繁
        if i < len(users):
            print("⏱️ 等待3秒后处理下一个用户...")
            import time
            time.sleep(3)

    result.finish()
    return result





class MSECSimpleClient:
    """MSEC客户端"""
    
    def __init__(self, username: str, password: str, enable_token_storage: bool = True,
                 captcha_token: Optional[str] = None, enable_auto_captcha: bool = False,
                 max_captcha_refresh: int = 2):
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.jwt_token = None
        self.enable_token_storage = enable_token_storage

        # 验证码识别配置
        self.captcha_token = captcha_token
        self.enable_auto_captcha = enable_auto_captcha
        self.max_captcha_refresh = max_captcha_refresh

        # 基础配置
        self.base_url = "https://msec.nsfocus.com"
        self.timeout = 30

        # Token存储
        if self.enable_token_storage:
            self.token_storage = UnifiedTokenStorage("msec_tokens.json")
            # 尝试加载已保存的Token
            self._load_saved_token()

        # 设置请求头
        self.session.headers.update({
            'Host': 'msec.nsfocus.com',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': 'https://msec.nsfocus.com',
            'Referer': 'https://msec.nsfocus.com/auth/login',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    def _load_saved_token(self) -> bool:
        """加载已保存的Token"""
        if not self.enable_token_storage:
            return False

        try:
            token = self.token_storage.load_token(self.username, self.password)
            if token:
                self.jwt_token = token
                self.session.headers['Authorization'] = token
                print("✅ 已加载保存的Token")
                return True
        except Exception as e:
            print(f"⚠️ 加载Token失败: {e}")

        return False

    def _save_token(self, token: str, expire_time: Optional[int] = None) -> bool:
        """保存Token到本地"""
        if not self.enable_token_storage:
            return False

        try:
            return self.token_storage.save_token(
                self.username,
                token,
                self.password,
                expire_time
            )
        except Exception as e:
            print(f"⚠️ 保存Token失败: {e}")
            return False

    def is_logged_in(self) -> bool:
        """检查是否已登录（有有效Token）"""
        if not self.jwt_token:
            return False

        if self.enable_token_storage:
            return self.token_storage.is_token_valid(self.username, self.password)

        return True

    def get_token_info(self) -> Optional[Dict[str, Any]]:
        """获取Token信息"""
        if not self.enable_token_storage:
            return None

        return self.token_storage.get_token_info(self.username, self.password)

    def logout(self) -> bool:
        """登出并清除Token"""
        self.jwt_token = None
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']

        if self.enable_token_storage:
            return self.token_storage.delete_token(self.username)

        return True

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        url = self.base_url + endpoint
        
        try:
            if method.upper() == 'POST':
                response = self.session.post(url, json=data or {}, timeout=self.timeout)
            else:
                response = self.session.get(url, timeout=self.timeout)
            
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def get_captcha(self) -> Tuple[Optional[str], Optional[str]]:
        """获取验证码"""
        print("🔍 正在获取验证码...")
        response = self._make_request('POST', '/backend_api/account/captcha')
        
        if not response:
            return None, None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取验证码失败: {data}")
                return None, None
            
            captcha_data = data.get('data', {})
            captcha_id = captcha_data.get('id')
            captcha_image = captcha_data.get('captcha')
            
            if captcha_id and captcha_image:
                print(f"🔍 验证码图片: {captcha_image}")
                print(f"📋 验证码ID: {captcha_id}")
                return captcha_id, captcha_image
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析验证码响应失败: {e}")
        
        return None, None

    def _recognize_captcha(self, captcha_image: str) -> Optional[str]:
        """
        使用第三方API识别验证码

        Args:
            captcha_image: base64编码的验证码图片

        Returns:
            识别结果，失败返回None
        """
        if not self.enable_auto_captcha or not self.captcha_token:
            return None

        try:
            print("🤖 正在自动识别验证码...")

            url = "http://api.jfbym.com/api/YmServer/customApi"
            data = {
                "token": self.captcha_token,
                "type": "50103",  # 验证码类型
                "image": captcha_image,
            }
            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)
            result = response.json()

            if result.get("code") == 10000:
                captcha_result = result.get("data", {}).get("data", "")
                print(f"🤖 验证码识别成功: {captcha_result}")
                return captcha_result
            else:
                error_msg = self._get_captcha_error_msg(result.get("code", 0))
                print(f"❌ 验证码识别失败: {error_msg}")
                return None

        except Exception as e:
            print(f"❌ 验证码识别异常: {e}")
            return None

    def _get_captcha_error_msg(self, code: int) -> str:
        """获取验证码识别错误信息"""
        error_codes = {
            10000: "识别成功",
            10001: "参数错误",
            10002: "余额不足",
            10003: "无此访问权限",
            10004: "无此验证类型",
            10005: "网络拥塞",
            10006: "数据包过载",
            10007: "服务繁忙",
            10008: "网络错误，请稍后重试",
            10009: "结果准备中，请稍后再试",
            10010: "请求结束"
        }
        return error_codes.get(code, f"未知错误(代码: {code})")

    def login(self, captcha_id: str, captcha_answer: str) -> bool:
        """用户登录"""
        print("🔐 正在登录...")
        
        login_data = {
            "captcha_answer": captcha_answer,
            "captcha_id": captcha_id,
            "password": self.password,
            "username": self.username
        }
        
        response = self._make_request('POST', '/backend_api/account/login', login_data)
        
        if not response:
            return False
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 登录失败: {data}")
                return False
            
            token = data.get('data', {}).get('token')
            if token:
                self.jwt_token = token
                self.session.headers['Authorization'] = token

                # 尝试解析Token过期时间并保存
                expire_time = None
                try:
                    import jwt as jwt_lib
                    decoded = jwt_lib.decode(token, options={"verify_signature": False})
                    expire_time = decoded.get('exp')
                except:
                    # 如果解析失败，设置默认过期时间（24小时后）
                    import time
                    expire_time = int(time.time()) + 24 * 3600

                # 保存Token到本地
                self._save_token(token, expire_time)

                print("✅ 登录成功!")
                return True
            else:
                print("❌ 未获取到token")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ 解析登录响应失败: {e}")
            return False
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取用户信息")
            return None
        
        print("👤 正在获取用户信息...")
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/'
        
        response = self._make_request('POST', '/backend_api/account/info')
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取用户信息失败: {data}")
                return None
            
            user_info = data.get('data', {}).get('user', {})
            if user_info:
                print(f"👤 用户: {user_info.get('username')} (ID: {user_info.get('id')})")
                return user_info
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析用户信息响应失败: {e}")
        
        return None
    
    def get_points(self) -> Optional[Dict[str, Any]]:
        """获取积分信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取积分信息")
            return None
        
        print("💰 正在获取积分信息...")
        response = self._make_request('POST', '/backend_api/point/common/get')
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取积分信息失败: {data}")
                return None
            
            points_data = data.get('data', {})
            if 'accrued' in points_data and 'total' in points_data:
                print(f"💰 积分: {points_data.get('accrued')}/{points_data.get('total')}")
                return points_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析积分信息响应失败: {e}")
        
        return None
    
    def get_roles(self) -> Optional[Dict[str, Any]]:
        """获取用户角色信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取角色信息")
            return None
        
        print("🎭 正在获取角色信息...")
        role_data = {"limit": 100, "offset": 0}
        
        response = self._make_request('POST', '/backend_api/rbac/role/self/list', role_data)
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取角色信息失败: {data}")
                return None
            
            roles_data = data.get('data', {})
            role_list = roles_data.get('list', [])
            
            if role_list:
                role_names: list[str] = []
                for role in role_list:
                    role_name = role.get('role_name', '未知角色')
                    # 处理Unicode编码
                    if isinstance(role_name, str) and '\\u' in role_name:
                        try:
                            role_name = role_name.encode().decode('unicode_escape')
                        except:
                            pass
                    
                    expire_time = role.get('expire_time', 0)
                    if expire_time == 0:
                        role_names.append(f"{role_name}(永久)")
                    else:
                        # 将时间戳转换为年月日格式
                        try:
                            from datetime import datetime
                            expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d')
                            role_names.append(f"{role_name}(至{expire_date})")
                        except:
                            role_names.append(f"{role_name}(限时)")
                
                print(f"🎭 角色: {', '.join(role_names)}")
            else:
                print("🎭 角色: 无")
            
            return roles_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析角色信息响应失败: {e}")
        
        return None
    
    def get_checkin_history(self, start_date: str, days: int = 5) -> Optional[Dict[str, Any]]:
        """获取签到历史"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取签到历史")
            return None
        
        print(f"📅 正在获取签到历史 (从 {start_date} 开始，{days} 天)...")
        history_data: Dict[str, Any] = {"start_date": start_date, "days": days}
        
        response = self._make_request('POST', '/backend_api/checkin/history', history_data)
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取签到历史失败: {data}")
                return None
            
            history_data = data.get('data', {})
            records_list = history_data.get('records_list', {})
            total_records = records_list.get('total', 0)
            checkin_list = records_list.get('list', [])

            print(f"📅 本周签到记录: {total_records}次")

            # 显示具体的签到时间
            if checkin_list:
                print("📅 签到时间明细:")
                for record in checkin_list:
                    checkin_time = record.get('checkin_time', '')
                    if checkin_time:
                        try:
                            # 将时间戳转换为可读格式
                            from datetime import datetime
                            if isinstance(checkin_time, (int, float)):
                                # 检查是否为毫秒级时间戳（13位数字）
                                if checkin_time > 9999999999:  # 大于10位数字，可能是毫秒
                                    timestamp = checkin_time / 1000  # 转换为秒
                                else:
                                    timestamp = checkin_time

                                # 转换为datetime对象
                                dt = datetime.fromtimestamp(timestamp)

                                # 获取星期几（中文）
                                weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                                weekday_cn = weekdays[dt.weekday()]

                                # 格式化时间显示
                                checkin_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                                print(f"   • {checkin_date} ({weekday_cn})")
                            else:
                                # 如果已经是字符串格式
                                checkin_date = str(checkin_time)
                                print(f"   • {checkin_date}")
                        except Exception as e:
                            print(f"   • {checkin_time} (时间格式错误)")

            return history_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析签到历史响应失败: {e}")
        
        return None
    
    def checkin(self) -> bool:
        """执行签到"""
        if not self.jwt_token:
            print("❌ 未登录，无法签到")
            return False
        
        print("📝 正在执行签到...")
        response = self._make_request('POST', '/backend_api/checkin/checkin')
        
        if not response:
            return False
        
        try:
            data = response.json()
            status = data.get('status')
            
            if status == 200:
                print("✅ 签到成功！")
                return True
            elif status == 400:
                print("⚠️ 今日已签到")
                return True
            else:
                print(f"❌ 签到失败 (状态: {status})")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ 解析签到响应失败: {e}")
            return False
    
    def user_login(self, max_attempts: int = 3, max_captcha_refresh: int = 2) -> bool:
        """
        用户登录（支持验证码刷新和重试）

        Args:
            max_attempts: 最大登录尝试次数
            max_captcha_refresh: 每次登录尝试中验证码刷新的最大次数
        """
        # 首先检查是否已有有效Token
        if self.is_logged_in():
            print("✅ 检测到有效Token，无需重新登录")
            return True

        for attempt in range(max_attempts):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次登录尝试...")

            # 在每次登录尝试中，支持验证码刷新
            login_success = self._attempt_login_with_captcha_refresh(max_captcha_refresh)

            if login_success:
                return True
            else:
                if attempt < max_attempts - 1:
                    print(f"❌ 登录失败，还有 {max_attempts - attempt - 1} 次机会")
                    print("🔄 将刷新验证码重新尝试...")

        print("❌ 登录失败，已达到最大尝试次数")
        return False

    def _attempt_login_with_captcha_refresh(self, max_refresh: int = 2) -> bool:
        """
        单次登录尝试，支持验证码刷新

        Args:
            max_refresh: 验证码刷新的最大次数

        Returns:
            登录是否成功
        """
        for refresh_count in range(max_refresh + 1):  # +1 因为第一次不算刷新
            if refresh_count > 0:
                print(f"🔄 刷新验证码 (第{refresh_count}次)")

            # 获取验证码
            captcha_id, captcha_image = self.get_captcha()
            if not captcha_id:
                print("❌ 验证码获取失败")
                if refresh_count < max_refresh:
                    continue  # 尝试刷新
                else:
                    return False

            # 尝试识别验证码（自动或手动）
            captcha_answer = self._get_captcha_answer(captcha_image, refresh_count)
            if not captcha_answer:
                if refresh_count < max_refresh:
                    print("� 验证码获取失败，尝试刷新...")
                    continue
                else:
                    return False

            # 尝试登录
            if self.login(captcha_id, captcha_answer):
                return True
            else:
                print("❌ 验证码错误")
                if refresh_count < max_refresh:
                    print("🔄 将刷新验证码重新尝试...")
                    continue
                else:
                    return False

        return False

    def _get_captcha_answer(self, captcha_image: Optional[str], refresh_count: int) -> Optional[str]:
        """
        获取验证码答案（自动识别或手动输入）

        Args:
            captcha_image: 验证码图片base64
            refresh_count: 当前刷新次数

        Returns:
            验证码答案，失败返回None
        """
        captcha_answer = None

        # 尝试自动识别验证码
        if self.enable_auto_captcha and captcha_image:
            captcha_answer = self._recognize_captcha(captcha_image)

            # 如果自动识别成功，直接返回
            if captcha_answer:
                return captcha_answer

            # 自动识别失败的提示
            if refresh_count == 0:
                print("🤖 自动识别失败，尝试刷新验证码...")
                return None  # 触发刷新
            else:
                print("🤖 多次自动识别失败，转为手动输入")

        # 手动输入验证码
        try:
            print("🔤 请查看验证码图片并手动输入:")
            if captcha_image:
                print(f"🖼️ 验证码图片: {captcha_image}")

            captcha_answer = input("请输入验证码: ").strip()
            if not captcha_answer:
                print("⚠️ 验证码不能为空")
                return None

            return captcha_answer

        except KeyboardInterrupt:
            print("\n用户取消了输入")
            return None
    
    def run_full_process(self) -> bool:
        """运行完整流程"""
        print("🚀 开始MSEC自动签到流程")
        
        # 1. 登录（使用配置的验证码刷新次数）
        if not self.user_login(max_captcha_refresh=self.max_captcha_refresh):
            return False
        
        # 2. 获取用户信息
        self.get_user_info()
        
        # 3. 获取积分信息
        self.get_points()
        
        # 4. 获取角色信息
        self.get_roles()
        
        # 5. 获取签到历史
        start_date, days = get_week_checkin_params()
        self.get_checkin_history(start_date, days)
        
        # 6. 执行签到
        success = self.checkin()
        
        if success:
            print("🎉 签到流程完成！")
        else:
            print("❌ 签到流程失败")
        
        return success


def main():
    """主函数 - 从环境变量读取账号密码进行批量签到"""
    print("MSEC自动签到脚本")
    print("=" * 40)

    # 从环境变量读取用户配置
    env_users = parse_env_users()
    yunma_token = get_yunma_token()

    if not env_users:
        print("❌ 未检测到环境变量配置")
        print("\n📋 请设置以下环境变量:")
        print("   MSEC_USER=user1,user2,user3")
        print("   MSEC_PASS=pass1,pass2,pass3")
        print("   YUNMA_TOKEN=your_yunma_token")
        print("\n💡 示例:")
        print("   export MSEC_USER='alice,bob'")
        print("   export MSEC_PASS='pwd1,pwd2'")
        print("   export YUNMA_TOKEN='token123'")
        return False

    print(f"🔍 检测到环境变量配置: {len(env_users)} 个用户")
    print("📋 用户列表:")
    for i, (username, _) in enumerate(env_users, 1):
        print(f"   {i}. {username}")

    print(f"\n🤖 云码Token: {'已配置' if yunma_token else '未配置'}")

    # 配置验证码刷新次数
    max_refresh = 3  # 默认刷新次数
    print(f"🔄 验证码刷新次数: {max_refresh}")

    # 执行多用户签到
    result = run_user_checkin(env_users, yunma_token, max_refresh)

    # 显示执行报告
    print(result.get_summary())

    # 返回执行结果
    return len(result.failed_users) == 0


if __name__ == "__main__":
    main()
