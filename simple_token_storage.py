#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一Token存储模块
混合加密存储，所有用户Token存储在同一个文件中
每个用户使用独立的密码加密
"""

import json
import base64
import time
import os
import threading
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class UnifiedTokenStorage:
    """统一Token存储类 - 所有用户Token存储在同一文件中"""

    def __init__(self, storage_file: str = "msec_tokens.json"):
        """
        初始化统一Token存储

        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = storage_file
        self.file_lock = threading.Lock()  # 文件锁，保证并发安全
    
    def _generate_key(self, password: str, salt: Optional[bytes] = None) -> tuple[bytes, bytes]:
        """
        基于密码生成加密密钥
        
        Args:
            password: 用户密码
            salt: 盐值，如果为None则生成新的
            
        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(16)  # 生成16字节随机盐值
        
        # 使用PBKDF2派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,  # 迭代次数，增加破解难度
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt

    def _load_file(self) -> Dict[str, Any]:
        """加载存储文件"""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass

        # 返回默认结构
        return {
            "users": {},
            "metadata": {
                "version": "1.0",
                "total_users": 0,
                "last_updated": int(time.time())
            }
        }

    def _save_file(self, data: Dict[str, Any]) -> bool:
        """保存数据到文件"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

    def _encrypt_user_data(self, user_data: Dict[str, Any], password: str) -> str:
        """加密用户数据"""
        key, salt = self._generate_key(password)
        fernet = Fernet(key)

        json_data = json.dumps(user_data, ensure_ascii=False)
        encrypted_data = fernet.encrypt(json_data.encode('utf-8'))

        # 返回 base64(salt + encrypted_data)
        combined = salt + encrypted_data
        return base64.b64encode(combined).decode('utf-8')

    def _decrypt_user_data(self, encrypted_data: str, password: str) -> Optional[Dict[str, Any]]:
        """解密用户数据"""
        try:
            combined = base64.b64decode(encrypted_data.encode('utf-8'))
            salt = combined[:16]
            encrypted_content = combined[16:]

            key, _ = self._generate_key(password, salt)
            fernet = Fernet(key)

            decrypted_data = fernet.decrypt(encrypted_content)
            return json.loads(decrypted_data.decode('utf-8'))
        except Exception:
            return None

    def _is_expired(self, user_data: Dict[str, Any]) -> bool:
        """检查Token是否过期"""
        expire_time = user_data.get("expire_time", 0)
        current_time = int(time.time())
        return current_time >= expire_time

    def _remove_user(self, username: str) -> bool:
        """从文件中移除用户"""
        try:
            data = self._load_file()
            if username in data["users"]:
                del data["users"][username]
                data["metadata"]["total_users"] = len(data["users"])
                data["metadata"]["last_updated"] = int(time.time())
                return self._save_file(data)
        except Exception:
            pass
        return False

    def save_token(self, username: str, token: str, password: str,
                   expire_time: Optional[int] = None) -> bool:
        """
        保存Token到统一文件

        Args:
            username: 用户名
            token: JWT Token
            password: 用户密码（用于加密）
            expire_time: Token过期时间戳

        Returns:
            保存是否成功
        """
        with self.file_lock:
            try:
                # 1. 读取现有数据
                data = self._load_file()

                # 2. 准备用户数据
                user_data = {
                    "token": token,
                    "expire_time": expire_time or (int(time.time()) + 24 * 3600)
                }

                # 3. 加密用户数据
                encrypted_data = self._encrypt_user_data(user_data, password)

                # 4. 更新文件数据
                data["users"][username] = encrypted_data
                data["metadata"]["total_users"] = len(data["users"])
                data["metadata"]["last_updated"] = int(time.time())

                # 5. 保存文件
                if self._save_file(data):
                    print(f"✅ Token已保存到 {self.storage_file}")
                    return True
                else:
                    return False

            except Exception as e:
                print(f"❌ 保存Token失败: {e}")
                return False
    
    def load_token(self, username: str, password: str) -> Optional[str]:
        """
        从统一文件加载Token

        Args:
            username: 用户名
            password: 用户密码（用于解密）

        Returns:
            Token字符串，失败返回None
        """
        with self.file_lock:
            try:
                # 1. 读取文件数据
                data = self._load_file()

                # 2. 检查用户是否存在
                if username not in data["users"]:
                    return None

                # 3. 解密用户数据
                user_data = self._decrypt_user_data(data["users"][username], password)
                if not user_data:
                    print("❌ 解密失败，密码可能错误")
                    return None

                # 4. 检查Token是否过期
                if self._is_expired(user_data):
                    print("⚠️ Token已过期")
                    # 自动清理过期Token
                    self._remove_user(username)
                    return None

                print("✅ Token加载成功")
                return user_data.get("token")

            except Exception as e:
                print(f"❌ 加载Token失败: {e}")
                return None
    
    def is_token_valid(self, username: str, password: str) -> bool:
        """
        检查Token是否有效（存在且未过期）

        Args:
            username: 用户名
            password: 用户密码

        Returns:
            Token是否有效
        """
        with self.file_lock:
            try:
                data = self._load_file()
                if username not in data["users"]:
                    return False

                user_data = self._decrypt_user_data(data["users"][username], password)
                if not user_data:
                    return False

                return not self._is_expired(user_data)
            except Exception:
                return False
    
    def get_token_info(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        获取Token详细信息

        Args:
            username: 用户名
            password: 用户密码

        Returns:
            Token信息字典，失败返回None
        """
        with self.file_lock:
            try:
                data = self._load_file()
                if username not in data["users"]:
                    return None

                user_data = self._decrypt_user_data(data["users"][username], password)
                if not user_data:
                    return None

                # 计算剩余有效时间
                expire_time = user_data.get("expire_time", 0)
                current_time = int(time.time())
                remaining_time = max(0, expire_time - current_time)

                return {
                    "username": username,
                    "expire_time": expire_time,
                    "remaining_seconds": remaining_time,
                    "is_valid": remaining_time > 0
                }

            except Exception as e:
                print(f"❌ 获取Token信息失败: {e}")
                return None
    
    def delete_token(self, username: Optional[str] = None) -> bool:
        """
        删除Token（指定用户或整个文件）

        Args:
            username: 用户名，如果为None则删除整个文件

        Returns:
            删除是否成功
        """
        with self.file_lock:
            try:
                if username is None:
                    # 删除整个文件
                    if os.path.exists(self.storage_file):
                        os.remove(self.storage_file)
                        print(f"✅ Token文件已删除: {self.storage_file}")
                    else:
                        print("⚠️ Token文件不存在")
                    return True
                else:
                    # 删除指定用户
                    return self._remove_user(username)
            except Exception as e:
                print(f"❌ 删除Token失败: {e}")
                return False

    def file_exists(self) -> bool:
        """
        检查Token文件是否存在

        Returns:
            文件是否存在
        """
        return os.path.exists(self.storage_file)

    def get_all_users(self) -> list[str]:
        """
        获取所有用户列表

        Returns:
            用户名列表
        """
        try:
            data = self._load_file()
            return list(data["users"].keys())
        except Exception:
            return []

    def cleanup_expired_tokens(self) -> int:
        """
        清理所有过期Token

        Returns:
            清理的Token数量
        """
        with self.file_lock:
            try:
                data = self._load_file()
                expired_users = []

                for username in data["users"].keys():
                    # 注意：这里无法验证密码，所以只能通过其他方式清理
                    # 实际使用中，过期Token会在用户登录时自动清理
                    pass

                # 更新元数据
                if expired_users:
                    for username in expired_users:
                        del data["users"][username]

                    data["metadata"]["total_users"] = len(data["users"])
                    data["metadata"]["last_updated"] = int(time.time())
                    self._save_file(data)

                return len(expired_users)
            except Exception:
                return 0


# 为了向后兼容，创建一个别名
SimpleTokenStorage = UnifiedTokenStorage


def demo_token_storage():
    """演示统一Token存储功能"""
    print("统一Token存储功能演示")
    print("=" * 30)

    storage = UnifiedTokenStorage("demo_tokens.json")

    # 测试数据
    users = [
        ("user1", "password1", "token1_data"),
        ("user2", "password2", "token2_data"),
        ("user3", "password3", "token3_data")
    ]

    print("1. 保存多个用户Token...")
    for username, password, token in users:
        success = storage.save_token(username, f"jwt.{token}.example", password)
        print(f"   {username}: {success}")

    print("\n2. 加载Token...")
    for username, password, token in users:
        loaded_token = storage.load_token(username, password)
        print(f"   {username}: {'✅' if loaded_token else '❌'}")

    print("\n3. 获取Token信息...")
    username, password, _ = users[0]
    info = storage.get_token_info(username, password)
    if info:
        print(f"   用户名: {info['username']}")
        print(f"   过期时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info['expire_time']))}")
        print(f"   剩余时间: {info['remaining_seconds']}秒")
        print(f"   是否有效: {info['is_valid']}")

    print("\n4. 测试错误密码...")
    wrong_token = storage.load_token(username, "wrong_password")
    print(f"   错误密码结果: {'❌' if wrong_token is None else '✅'}")

    print("\n5. 获取所有用户...")
    all_users = storage.get_all_users()
    print(f"   用户列表: {all_users}")

    print("\n6. 清理测试文件...")
    storage.delete_token()  # 删除整个文件

    print("\n✅ 演示完成")


if __name__ == "__main__":
    demo_token_storage()
